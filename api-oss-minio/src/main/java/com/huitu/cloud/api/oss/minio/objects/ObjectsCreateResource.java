package com.huitu.cloud.api.oss.minio.objects;


import com.huitu.cloud.api.oss.api.*;
import com.huitu.cloud.api.oss.api.ApiResponse;
import com.huitu.cloud.api.oss.minio.entities.FileVo;
import com.huitu.cloud.api.oss.util.MinioUtils;
import com.huitu.datasource.DataSourceContextHolder;
import io.minio.MinioClient;
import io.minio.errors.ErrorResponseException;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.UUID;

@RestController
@RequestMapping("/api/oss/minio/objects")
@Api(tags = "文件服务")
public class ObjectsCreateResource extends AbstractApiResource implements ApiResource {

    @Autowired
    private MinioClient minioClient;

    @Override
    public String getUuid() {
        return "3714de02-96a0-44aa-af2a-da96c754156b";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }
    @ApiOperation(value = "文件上传 指定用户空间" , notes = "文件上传 指定用户空间")
    @ApiImplicitParam(name = "bucketname", value = "文件路径",required = true, dataType = "String")
    @PostMapping(value="{bucketname}",headers = "content-type=multipart/form-data")
    public ResponseEntity<ApiResponse> create(@PathVariable("bucketname") String bucketname,
                                              @ApiParam(value="file",required = true) MultipartFile file) throws Exception {


        String fileName=file.getOriginalFilename();
        int lastIndex=fileName.lastIndexOf(".");
        String fileType=fileName.substring(lastIndex+1);
        String fileAcceptTypeArr [] = {"bmp","jpg","jpeg","png","tiff","gif","pcx","tga","exif","fpx","svg","psd","cdr","pcd","dxf",
                "ufo","eps","ai","raw","wmf","txt","doc","docx","xls","xlsx","rtf","wpd","pdf","ppt","mp4","avi","mov","wmv","asf","navi","3gp","mkv","f4v","rmvb",
                "webm","mp3","wma","wav","mod","ra","cd","md","asf","aac","vqf","ape","mid","ogg","m4a","vqf","amr"};
        boolean existFileType = isExistFileType(fileAcceptTypeArr, fileType);
        if (!existFileType) {
            return ResponseEntity.ok(
                    new FailureResponse(this,501,  "faild[File type failed]")
            );
        }
        String newFileName= fileName.substring(0,lastIndex)+ UUID.randomUUID()+fileName.substring(lastIndex);
        //按照文件类型创建用户空间
        if(StringUtils.isEmpty(bucketname)){
            bucketname=fileType.toLowerCase();
        }
        boolean found = minioClient.bucketExists(bucketname);
        if (!found) {
//            return ResponseEntity.notFound().build();
            minioClient.makeBucket(bucketname);
        }
        LocalDateTime localDateTime= LocalDateTime.now();

        String year=localDateTime.getYear()+"";
        String newpath=year+"";
        newpath= MinioUtils.ensureObjectPath(minioClient,bucketname,newpath);
        String objectName =  newpath+newFileName;
        String contentType = file.getContentType();

        InputStream in = file.getInputStream();

        contentType = StringUtils.isEmpty(contentType) ? MediaType.APPLICATION_OCTET_STREAM.toString() : contentType;

        int current = 0;
        boolean success = false;
        int retry = 3;
        while (!success && current < retry) {
            try {
                minioClient.putObject(bucketname, objectName, in,file.getSize(),null,null, contentType);
                success = true;
            } catch (ErrorResponseException e) {
                //todo logger the exception
                current++;
                Thread.sleep(200);
            }
        }
        FileVo fileVo=new FileVo();
        fileVo.setFilename(fileName);
        fileVo.setFilepath(objectName);
        if (current == retry) {
            return ResponseEntity.ok(
                    new FailureResponse(this,501,  "faild[out of retrys]")
            );
        }

        return ResponseEntity.ok(
                new SuccessResponse<>(this, "ok", fileVo)
        );
    }
    @ApiOperation(value = "上传文档" , notes = "上传文档到列用户存储空间")
    @PostMapping(value="",headers = "content-type=multipart/form-data" )
    public ResponseEntity<ApiResponse> create(@ApiParam(value="file",required = true) MultipartFile file) throws Exception {
        String fileName=file.getOriginalFilename();
        int lastIndex=fileName.lastIndexOf(".");
        String fileType=fileName.substring(lastIndex+1);
        String fileAcceptTypeArr [] = {"bmp","jpg","jpeg","png","tiff","gif","pcx","tga","exif","fpx","svg","psd","cdr","pcd","dxf",
                "ufo","eps","ai","raw","wmf","txt","doc","docx","xls","xlsx","rtf","wpd","pdf","ppt","mp4","avi","mov","wmv","asf","navi","3gp","mkv","f4v","rmvb",
                "webm","mp3","wma","wav","mod","ra","cd","md","asf","aac","vqf","ape","mid","ogg","m4a","vqf","amr"};
        boolean existFileType = isExistFileType(fileAcceptTypeArr, fileType);
        if (!existFileType) {
            return ResponseEntity.ok(
                    new FailureResponse(this,501,  "faild[File type failed]")
            );
        }
        String newFileName= fileName.substring(0,lastIndex)+ UUID.randomUUID()+fileName.substring(lastIndex);
        //按照文件类型创建用户空间
        String bucketname=fileType.toLowerCase();
        boolean found = minioClient.bucketExists(bucketname);
        if (!found) {
//            return ResponseEntity.notFound().build();
            minioClient.makeBucket(bucketname);
        }
        LocalDateTime localDateTime= LocalDateTime.now();

        String year=localDateTime.getYear()+"";
        String newpath=year+"";
        newpath= MinioUtils.ensureObjectPath(minioClient,bucketname,newpath);
        String objectName =  newpath+newFileName;
        String contentType = file.getContentType();

        InputStream in = file.getInputStream();

        contentType = StringUtils.isEmpty(contentType) ? MediaType.APPLICATION_OCTET_STREAM.toString() : contentType;

        int current = 0;
        boolean success = false;
        int retry = 3;
        while (!success && current < retry) {
            try {
                minioClient.putObject(bucketname, objectName, in,file.getSize(),null,null, contentType);
                success = true;
            } catch (ErrorResponseException e) {
                //todo logger the exception
                current++;
                Thread.sleep(200);
            }
        }
        FileVo fileVo=new FileVo();
        fileVo.setFilename(fileName);
        fileVo.setFilepath(objectName);
        if (current == retry) {
       /*     DataSourceContextHolder.push("");

            DataSourceContextHolder.poll();*/
            return ResponseEntity.ok(
                    new FailureResponse(this,501,  "faild[out of retrys]")
            );
        }

        return ResponseEntity.ok(
                new SuccessResponse<>(this, "ok", fileVo)
        );
    }
    public boolean isExistFileType(String[] arr, String targetValue) {
        for (String s : arr) {
            if (s.equals(targetValue)) {
                return true;
            }
        }
        return false;
    }
}