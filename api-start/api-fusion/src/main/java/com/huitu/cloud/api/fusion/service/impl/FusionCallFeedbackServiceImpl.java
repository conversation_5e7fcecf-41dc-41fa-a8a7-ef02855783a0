package com.huitu.cloud.api.fusion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.huitu.cloud.api.fusion.entity.base.FusionManualConf;
import com.huitu.cloud.api.fusion.entity.base.FusionNoticeReceiver;
import com.huitu.cloud.api.fusion.entity.base.FusionWarningConf;
import com.huitu.cloud.api.fusion.entity.common.FusionCallStatus;
import com.huitu.cloud.api.fusion.entity.request.FusionCallStatisticsReq;
import com.huitu.cloud.api.fusion.entity.request.FusionManualConfirmReq;
import com.huitu.cloud.api.fusion.entity.request.FusionNoticeNotCertReq;
import com.huitu.cloud.api.fusion.entity.request.FusionNoticeReceiverReq;
import com.huitu.cloud.api.fusion.entity.response.FusionCallStatisticsResp;
import com.huitu.cloud.api.fusion.entity.response.FusionNoticeNotCertResp;
import com.huitu.cloud.api.fusion.entity.response.FusionNoticeReceiverResp;
import com.huitu.cloud.api.fusion.mapper.*;
import com.huitu.cloud.api.fusion.service.FusionCallFeedbackService;
import com.huitu.cloud.util.AdcdUtil;
import com.huitu.cloud.util.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.huitu.cloud.api.fusion.entity.common.FusionCallStatus.CONFIRMED;
import static com.huitu.cloud.api.fusion.entity.common.FusionCallStatus.UNCONFIRMED;

/**
 * 叫应反馈服务 实现类
 */
@Service
public class FusionCallFeedbackServiceImpl implements FusionCallFeedbackService {

    @Autowired
    private FusionCallFeedbackDao feedbackDao;

    @Autowired
    private FusionWarningConfDao warningConfDao;

    @Autowired
    private FusionManualConfDao manualConfDao;

    @Autowired
    private FusionWarningRecordDao recordDao;

    @Autowired
    private FusionWarningFlowDao flowDao;

    @Override
    public List<FusionNoticeReceiverResp> getNotifiedReceiverList(FusionNoticeReceiverReq request) {
        return getNoticeReceiverList(request.getPlatformId(), CONFIRMED, request.toQuery());
    }

    @Override
    public List<FusionNoticeReceiverResp> getNotNoticeReceiverList(String platformId) {
        FusionManualConf conf = manualConfDao.getConf("9");
        if (null == conf || "0".equals(conf.getManualFlag())) {
            return new ArrayList<>();
        }
        Date etm = DateUtil.offsetMinute(new Date(), -conf.getManualDelay());
        Date stm = DateUtil.offsetMinute(etm, -conf.getManualExpired());
        Map<String, Object> params = new HashMap<>();
        params.put("stm", stm);
        params.put("etm", etm);
        return getNoticeReceiverList(platformId, UNCONFIRMED, params);
    }


    private List<FusionNoticeReceiverResp> getNoticeReceiverList(String platformId, FusionCallStatus status, Map<String, Object> params) {
        FusionWarningConf conf = warningConfDao.getConf(platformId);
        if (null == conf || "0".equals(conf.getFlag())) {
            return new ArrayList<>();
        }
        String adcd = StrUtil.padAfter(platformId, 15, "0");
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("status", status.getCode());
        params.put("exclusions", conf.getExclusions());
        List<FusionNoticeReceiver> sourceList = feedbackDao.getNoticeReceiverList(params);
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<FusionNoticeReceiverResp> targetList = new ArrayList<>();
        Map<String, List<FusionNoticeReceiver>> groups = sourceList.stream().collect(Collectors.groupingBy(FusionNoticeReceiver::getPhoneNo));
        for (Map.Entry<String, List<FusionNoticeReceiver>> group : groups.entrySet()) {
            List<FusionNoticeReceiver> receivers = group.getValue();
            Optional<FusionNoticeReceiver> latest = receivers.stream().max(Comparator.comparing(FusionNoticeReceiver::getCreateTime));
            if (!latest.isPresent()) {
                continue;
            }
            FusionNoticeReceiverResp receiver = new FusionNoticeReceiverResp(latest.get());
            receiver.setCallIds(receivers.stream().map(FusionNoticeReceiver::getCallId).collect(Collectors.toSet()));
            receiver.setWarnIds(receivers.stream().map(FusionNoticeReceiver::getWarnId).collect(Collectors.toSet()));
            targetList.add(receiver);
        }
        if (UNCONFIRMED.equals(status)) {
            targetList.sort(Comparator.comparing(FusionNoticeReceiver::getCreateTime).reversed());
        } else if (CONFIRMED.equals(status)) {
            targetList.sort(Comparator.comparing(FusionNoticeReceiver::getConfirmTime).reversed());
        }

        return targetList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int manualConfirm(FusionManualConfirmReq request) {
        int result = 0;
        List<List<String>> callIdsList = ListUtils.splitList(new ArrayList<>(request.getCallIds()), 500);
        for (List<String> callIds : callIdsList) {
            result += feedbackDao.manualConfirm(callIds);
        }
        if (result > 0) {
            List<List<String>> warnIdsList = ListUtils.splitList(new ArrayList<>(request.getWarnIds()), 500);
            for (List<String> warnIds : warnIdsList) {
                recordDao.manualConfirm(warnIds);
                flowDao.manualConfirm(request.getConfirmBy(), warnIds);
            }
        }
        return result;
    }

    @Override
    public List<FusionCallStatisticsResp> getCallFailStatsList(FusionCallStatisticsReq request) {
        return feedbackDao.getCallFailStatsList(request.toQuery());
    }

    @Override
    public List<FusionNoticeNotCertResp> getNoticeNotCertList(FusionNoticeNotCertReq request) {
        Map<String, Object> params = request.toQuery();
        params.put("stm", request.getStm());
        params.put("etm", request.getEtm());
        return getNoticeNotCertList(request.getPlatformId(), params);
    }

    private List<FusionNoticeNotCertResp> getNoticeNotCertList(String platformId, Map<String, Object> params) {
        FusionWarningConf conf = warningConfDao.getConf(platformId);
        if (null == conf || "0".equals(conf.getFlag())) {
            return new ArrayList<>();
        }
        String adcd = StrUtil.padAfter(platformId, 15, "0");
        params.put("adcd", adcd);
        params.put("level", AdcdUtil.getAdLevel(adcd));
        params.put("exclusions", conf.getExclusions());
        List<FusionNoticeReceiver> sourceList = feedbackDao.getNoticeNotCertList(params);
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<FusionNoticeNotCertResp> targetList = new ArrayList<>();
        Map<String, List<FusionNoticeReceiver>> groups = sourceList.stream().collect(Collectors.groupingBy(FusionNoticeReceiver::getPhoneNo));
        for (Map.Entry<String, List<FusionNoticeReceiver>> group : groups.entrySet()) {
            List<FusionNoticeReceiver> receivers = group.getValue();
            Optional<FusionNoticeReceiver> latest = receivers.stream().max(Comparator.comparing(FusionNoticeReceiver::getCreateTime));
            if (!latest.isPresent()) {
                continue;
            }
            FusionNoticeNotCertResp receiver = new FusionNoticeNotCertResp(latest.get());
            receiver.setCallIds(receivers.stream().map(FusionNoticeReceiver::getCallId).collect(Collectors.toSet()));
            receiver.setWarnIds(receivers.stream().map(FusionNoticeReceiver::getWarnId).collect(Collectors.toSet()));
            receiver.setAdcdList(receivers.stream().map(FusionNoticeReceiver::getAdcd).collect(Collectors.toSet()));
            receiver.setAdnmList(receivers.stream().map(FusionNoticeReceiver::getAdnm).collect(Collectors.toSet()));
            receiver.setAdnms(String.join(",", receivers.stream()
                    .map(FusionNoticeReceiver::getAdnm)
                    .collect(Collectors.toSet())));
            targetList.add(receiver);
        }
        Comparator.comparing(FusionNoticeReceiver::getConfirmTime).reversed();

        return targetList;
    }
}
