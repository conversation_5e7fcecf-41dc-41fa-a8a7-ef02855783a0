package com.huitu.cloud.api.fusion.mapper;

import com.huitu.cloud.api.fusion.entity.base.FusionNoticeReceiver;
import com.huitu.cloud.api.fusion.entity.response.FusionCallStatisticsResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FusionCallFeedbackDao {

    /**
     * 获取人工通知接收人列表
     *
     * @param params 查询参数
     * @return 接收人列表
     */
    List<FusionNoticeReceiver> getNoticeReceiverList(@Param("map") Map<String, Object> params);

    /**
     * 预警人工确认
     *
     * @param callIds 叫应ID集合
     * @return 受影响的行数
     */
    int manualConfirm(@Param("callIds") List<String> callIds);

    /**
     * 预警叫应失败统计列表
     *
     * @param params 查询参数
     * @return 统计列表
     */
    List<FusionCallStatisticsResp> getCallFailStatsList(@Param("map") Map<String, Object> params);

    /**
     * 获取人工通知接收人列表
     *
     * @param params 查询参数
     * @return 接收人列表
     */
    List<FusionNoticeReceiver> getNoticeNotCertList(@Param("map") Map<String, Object> params);
}
